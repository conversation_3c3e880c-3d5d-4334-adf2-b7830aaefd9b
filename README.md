# WallHammer

A Flutter app to display and download wallpapers for coins.
Uses a Firebase backend to store user data and wallpapers.

## Local Setup

Recommended versions:
- Flutter v3.22.2 (Dart v3.4.3)
- JDK v21

If not done yet, setup Firebase emulators using:
``
firebase init emulators
``
Choose at least:
- Authentication Emulator
- Functions Emulator
- Firestore Emulator
- Database Emulator
- Storage Emulator

Start Firebase emulator:
``
firebase emulators:start --import exported --export-on-exit exported
``

Run app:
``
flutter run
    --dart-define=AD_REWARD_ANDROID=android_reward_ad_id
    --dart-define=AD_REWARD_IOS=ios_reward_ad_id
    --dart-define=AD_BANNER_ANDROID=android_banner_ad_id
    --dart-define=AD_BANNER_IOS=ios_banner_ad_id
``

## Deploy updated Firestore Cloud Functions

If there are updated cloud functions, run:
``
firebase deploy --only functions
``# wallpaper
