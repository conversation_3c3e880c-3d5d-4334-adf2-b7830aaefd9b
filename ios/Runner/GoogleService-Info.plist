<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>472654657611-bbocf9hdn1qgaoku8rodgpj9kg74vm62.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.472654657611-bbocf9hdn1qgaoku8rodgpj9kg74vm62</string>
	<key>API_KEY</key>
	<string>AIzaSyBOV_bph6JIFP01WSMWMIcYDwK3BOR9sf8</string>
	<key>GCM_SENDER_ID</key>
	<string>472654657611</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>dev.schloool.wallpaperHero</string>
	<key>PROJECT_ID</key>
	<string>wallhammer-5f01e</string>
	<key>STORAGE_BUCKET</key>
	<string>wallhammer-5f01e.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:472654657611:ios:a3885e1feef92a65f6b5ce</string>
	<key>ADMOB_APP_ID</key>
	<string>ca-app-pub-9147916407761104~6054141603</string>
</dict>
</plist>