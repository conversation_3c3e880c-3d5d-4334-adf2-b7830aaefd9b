const admin = require("firebase-admin");
const { onObjectFinalized, onObjectDeleted } = require("firebase-functions/v2/storage");
const { onDocumentDeleted } = require("firebase-functions/v2/firestore");
const AdmZip = require("adm-zip");
const path = require("path");

admin.initializeApp();

// Get the default bucket
const bucket = admin.storage().bucket();

exports.processUploadedFiles = onObjectFinalized(async (event) => {
  const file = event.data;
  if (!file || !file.name || !file.name.startsWith("uploads/")) return;

  if (file.metadata?.metadata?.isRenamed === "true") return;

  const fileName = path.basename(file.name);

  try {
    if (fileName.endsWith(".zip")) {
      await handleZipUpload(file.name);
      await bucket.file(file.name).delete();
    } else if (isImageFile(fileName)) {
      await processWallpaper(file.name, []);
      await bucket.file(file.name).delete();
    }
  } catch (error) {
    console.error("Error processing file:", fileName, error);
  }
});

exports.deleteWallpaperFromFirestore = onObjectDeleted(async (event) => {
  const file = event.data;
  if (!file || !file.name || !file.name.startsWith("wallpapers/")) return;

  const docId = path.basename(file.name, path.extname(file.name));

  try {
    await admin.firestore().collection("wallpapers").doc(docId).delete();
  } catch (error) {
    console.error("Error deleting Firestore document:", docId, error);
  }
});

exports.deleteWallpaperFromStorage = onDocumentDeleted("wallpapers/{docId}", async (event) => {
  const docId = event.params.docId;

  try {
    const [files] = await bucket.getFiles({ prefix: `wallpapers/${docId}` });

    if (!files.length) return;

    for (const f of files) {
      await f.delete();
    }
  } catch (error) {
    console.error("Error deleting storage files for wallpaper:", docId, error);
  }
});

async function handleZipUpload(filePath) {
  const tempFilePath = "/tmp/" + path.basename(filePath);

  try {
    await bucket.file(filePath).download({ destination: tempFilePath });

    const zip = new AdmZip(tempFilePath);
    const entries = zip.getEntries();

    for (const e of entries) {
      if (!e.isDirectory && isImageFile(e.entryName)) {
        const buf = e.getData();
        const newFilePath = `uploads/${path.basename(e.entryName)}`;

        await bucket.file(newFilePath).save(buf, {
          metadata: { contentType: "image/jpeg" }
        });

        await processWallpaper(newFilePath, path.basename(filePath).replace(".zip", "").split("_"));
      }
    }

    await bucket.file(filePath).delete();
  } catch (error) {
    console.error("Error processing ZIP upload:", filePath, error);
  }
}

async function processWallpaper(originalPath, categories) {
  const originalFile = bucket.file(originalPath);
  const docRef = admin.firestore().collection("wallpapers").doc();
  const docId = docRef.id;
  const ext = path.extname(originalPath);
  const newPath = `wallpapers/${docId}${ext}`;

  try {
    await originalFile.move(newPath);
    const newFile = bucket.file(newPath);

    await newFile.setMetadata({ metadata: { isRenamed: "true" } });

    await docRef.set({
      categories,
      downloads: 0,
      ratings: [],
      averageRating: 0,
      filePath: newPath,
    });
  } catch (error) {
    console.error("Error processing wallpaper:", originalPath, error);
  }
}

function isImageFile(filename) {
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(filename);
}
