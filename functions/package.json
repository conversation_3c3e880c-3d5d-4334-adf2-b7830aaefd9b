{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"adm-zip": "^0.5.16", "firebase-admin": "^12.6.0", "firebase-functions": "^6.3.0"}, "devDependencies": {"eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}