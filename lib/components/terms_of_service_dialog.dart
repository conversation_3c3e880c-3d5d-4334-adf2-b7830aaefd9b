import 'package:flutter/material.dart';

class TermsOfServiceDialog extends StatelessWidget {
  const TermsOfServiceDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Terms of Service',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSection(
              'Acceptance of Terms',
              [
                'By downloading, installing, or using WallHammer, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our app.',
              ],
            ),
            _buildSection(
              'Description of Service',
              [
                'WallHammer is a wallpaper application that provides users with access to a collection of wallpapers for personal use on their devices.',
                '• Free tier: Limited access to wallpapers with advertisements',
                '• Premium tier: Unlimited access to all wallpapers without advertisements',
              ],
            ),
            _buildSection(
              'User Accounts',
              [
                '• You must create an account to access premium features',
                '• You are responsible for maintaining the security of your account',
                '• You must provide accurate and complete information',
                '• You are responsible for all activities under your account',
              ],
            ),
            _buildSection(
              'Subscription Terms',
              [
                '• Premium subscriptions are billed through Google Play Store',
                '• Subscriptions automatically renew unless cancelled',
                '• You can cancel anytime through Google Play Store settings',
                '• No refunds for partial subscription periods',
                '• Subscription prices may change with notice',
              ],
            ),
            _buildSection(
              'Acceptable Use',
              [
                'You agree not to:',
                '• Use the app for any illegal or unauthorized purpose',
                '• Attempt to gain unauthorized access to our systems',
                '• Interfere with or disrupt the app\'s functionality',
                '• Reverse engineer or attempt to extract source code',
                '• Use the app to distribute malware or harmful content',
              ],
            ),
            _buildSection(
              'Intellectual Property',
              [
                '• Wallpapers are provided for personal use only',
                '• You may not redistribute or sell wallpapers',
                '• The app and its content are protected by copyright',
                '• Third-party wallpapers may have additional restrictions',
              ],
            ),
            _buildSection(
              'Privacy and Data',
              [
                '• Your privacy is important to us',
                '• We collect and use data as described in our Privacy Policy',
                '• By using the app, you consent to our data practices',
                '• You can request deletion of your data at any time',
              ],
            ),
            _buildSection(
              'Limitation of Liability',
              [
                '• The app is provided "as is" without warranties',
                '• We are not liable for any damages arising from app use',
                '• We do not guarantee uninterrupted or error-free service',
                '• Maximum liability is limited to the amount you paid for the service',
              ],
            ),
            _buildSection(
              'Termination',
              [
                '• We may terminate your account for violations of these terms',
                '• You may cancel your account at any time',
                '• Upon termination, your access to premium features will end',
                '• Some terms survive termination (privacy, liability, etc.)',
              ],
            ),
            _buildSection(
              'Changes to Terms',
              [
                '• We may update these terms from time to time',
                '• Significant changes will be communicated to users',
                '• Continued use constitutes acceptance of new terms',
                '• You can reject changes by discontinuing app use',
              ],
            ),
            _buildSection(
              'Governing Law',
              [
                '• These terms are governed by the laws of [Your Jurisdiction]',
                '• Disputes will be resolved in [Your Jurisdiction] courts',
                '• If any provision is invalid, the remainder remains in effect',
              ],
            ),
            _buildSection(
              'Contact Information',
              [
                'For questions about these Terms of Service, contact us at:',
                'Email: <EMAIL>',
                'Address: [Your Company Address]',
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Last updated: ${DateTime.now().year}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildSection(String title, List<String> points) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          ...points.map((point) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              point,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          )),
        ],
      ),
    );
  }
} 