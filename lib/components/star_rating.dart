import 'package:flutter/material.dart';

class StarRating extends StatelessWidget {
  static const maxStars = 5;

  final double rating;
  final double iconSize;

  const StarRating({
    required this.rating,
    this.iconSize = 16,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(maxStars, (i) {
        final isFilled = i < rating;
        return Icon(
          Icons.star,
          size: iconSize,
          color: isFilled ? Colors.amber : Colors.grey,
        );
      }),
    );
  }
}
