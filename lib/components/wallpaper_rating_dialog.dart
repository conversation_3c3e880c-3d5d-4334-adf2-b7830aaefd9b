import 'package:flutter/material.dart';

class WallpaperRatingDialog extends StatefulWidget {
  final Function(double) onRated;

  const WallpaperRatingDialog({required this.onRated, super.key});

  @override
  State<WallpaperRatingDialog> createState() => _WallpaperRatingDialogState();
}

class _WallpaperRatingDialogState extends State<WallpaperRatingDialog> {
  double _selectedRating = 3.0;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      title: const Text("Rate this Wallpaper"),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text("Select a rating from 1 to 5 stars."),
          const SizedBox(height: 10),
          Slider(
            value: _selectedRating,
            min: 1,
            max: 5,
            divisions: 4,
            onChanged: (value) => setState(() => _selectedRating = value),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              return Icon(
                index < _selectedRating ? Icons.star : Icons.star_border,
                color: Colors.amber,
              );
            }),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          style: ButtonStyle(
            textStyle: WidgetStatePropertyAll(
              Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey,
              ),
            )
          ),
          child: const Text("Don't rate"),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onRated(_selectedRating);
            Navigator.pop(context);
          },
          child: const Text("Submit"),
        ),
      ],
    );
  }
}
