import 'package:flutter/material.dart';
import 'package:wallhammer/components/wallpaper_network_image.dart';
import 'package:wallhammer/model/wallpaper.dart';

import '../screen/wallpaper_detail_screen.dart';

class WallpaperOverviewItem extends StatelessWidget {
  final Wallpaper wallpaper;
  final List<Wallpaper> dataset;

  const WallpaperOverviewItem({
    required this.wallpaper,
    required this.dataset,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        print("HE");
        Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => WallpaperDetailScreen(
          wallpaper: wallpaper,
          dataset: dataset,
        ),
      ));
      },
      child: ClipRRect(
        borderRadius: BorderRadius.zero,
        child: WallpaperNetworkImage(
          wallpaper: wallpaper,
          fit: BoxFit.cover, // Ensures it fills the grid cell
        ),
      ),
    );
  }
}
