import 'package:flutter/material.dart';

class PrivacyPolicyDialog extends StatelessWidget {
  const PrivacyPolicyDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Privacy Policy',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSection(
              'Information We Collect',
              [
                '• Account information (email, display name)',
                '• Usage data (wallpapers downloaded, preferences)',
                '• Device information (device type, OS version)',
                '• Payment information (processed securely through Google Play)',
              ],
            ),
            _buildSection(
              'How We Use Your Information',
              [
                '• Provide and maintain our wallpaper service',
                '• Process subscription payments and manage your account',
                '• Improve our app and user experience',
                '• Send important updates and notifications',
                '• Provide customer support',
              ],
            ),
            _buildSection(
              'Data Storage and Security',
              [
                '• Your data is stored securely using Firebase services',
                '• We use industry-standard encryption to protect your information',
                '• We do not sell, trade, or rent your personal information',
                '• Data is retained only as long as necessary to provide our services',
              ],
            ),
            _buildSection(
              'Third-Party Services',
              [
                '• Google Play Services (for payments and authentication)',
                '• Firebase (for data storage and analytics)',
                '• Google Analytics (for app performance monitoring)',
              ],
            ),
            _buildSection(
              'Your Rights',
              [
                '• Access your personal data',
                '• Request deletion of your account and data',
                '• Opt out of marketing communications',
                '• Contact us with privacy concerns',
              ],
            ),
            _buildSection(
              'Children\'s Privacy',
              [
                '• Our app is not intended for children under 13',
                '• We do not knowingly collect personal information from children under 13',
                '• If you believe we have collected such information, please contact us',
              ],
            ),
            _buildSection(
              'Changes to This Policy',
              [
                '• We may update this privacy policy from time to time',
                '• You will be notified of significant changes',
                '• Continued use of the app constitutes acceptance of changes',
              ],
            ),
            _buildSection(
              'Contact Us',
              [
                'If you have any questions about this Privacy Policy, please contact us at:',
                'Email: <EMAIL>',
                'Address: SL-Services KG, Hauptstraße 4, Mödling - 2340, Austria (AT)',
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Last updated: ${DateTime.now().year}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildSection(String title, List<String> points) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          ...points.map((point) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              point,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          )),
        ],
      ),
    );
  }
} 