import 'package:flutter/material.dart';
import 'package:wallhammer/screen/user_screen.dart';

import '../screen/wallpaper_list_screen.dart';

class NavigationMenu extends StatelessWidget {
  const NavigationMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: const Color(0xFF1A1A1A),
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Wallhammer',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 30,
                    fontWeight: FontWeight.bold
                  ),
                ),
                Image.asset(
                  'assets/img/wallhammer_raw.png',
                  height: 75,
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.image, color: Colors.white),
            title: Text('Wallpapers', style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white,
            )),
            onTap: () => Navigator.of(context).pushReplacement(MaterialPageRoute(
                builder: (context) => const WallpaperListScreen())),
          ),
          ListTile(
            leading: const Icon(Icons.person, color: Colors.white),
            title: Text('My Profile', style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white,
            )),
            onTap: () => Navigator.of(context).pushReplacement(MaterialPageRoute(
                builder: (context) => const UserScreen())),
          ),
        ],
      ),
    );
  }
}
