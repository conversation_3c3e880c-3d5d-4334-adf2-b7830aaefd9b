import 'package:flutter/material.dart';
import 'package:wallhammer/util/string_util.dart';

class CategorySelector extends StatelessWidget {
  final Set<String> categories;
  final String? selectedCategory;
  final ValueChanged<String?> onSelected;

  const CategorySelector({
    required this.categories,
    required this.selectedCategory,
    required this.onSelected,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final allCategories = [...categories]..sort();
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: Row(
        children: [
          _buildCategoryChip(context, null, 'All'),
          const SizedBox(width: 8),
          ...allCategories.map((category) => Row(
            children: [
              _buildCategoryChip(context, category, category.capitalize()),
              const SizedBox(width: 8),
            ],
          )),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(BuildContext context, String? value, String label) {
    final bool isSelected = selectedCategory == value;
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onSelected(value),
      selectedColor: Theme.of(context).colorScheme.primary,
      backgroundColor: Colors.grey.shade200,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black87,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }
}
