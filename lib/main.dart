import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:wallhammer/theme.dart';
import 'package:wallhammer/util/authentication_util.dart';
import 'package:wallhammer/util/purchase_util.dart';

import 'firebase_options.dart';
import 'model/app_user.dart';
import 'screen/login_screen.dart';
import 'screen/wallpaper_list_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Set this to false to connect to production Firebase (to see wallpapers on emulator)
  // Set this to true to use local Firebase emulators (for development/testing)
  const bool useFirebaseEmulators = false;

  if (kDebugMode && useFirebaseEmulators) {
    // Use ******** for Android emulator (maps to host machine's localhost)
    // Use localhost for iOS simulator and other platforms
    if (defaultTargetPlatform == TargetPlatform.android) {
      await FirebaseAuth.instance.useAuthEmulator('********', 9099);
      FirebaseFirestore.instance.useFirestoreEmulator('********', 8081);
      FirebaseStorage.instance.useStorageEmulator('********', 9199);
    } else {
      await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
      FirebaseFirestore.instance.useFirestoreEmulator('localhost', 8081);
      FirebaseStorage.instance.useStorageEmulator('localhost', 9199);
    }
  }

  // Handles the user registration when the user is authenticated by creating
  // a new user document in the Firestore database.
  FirebaseAuth.instance.authStateChanges().listen((User? user) async {
    if (user != null) {
      await handleUserRegistration(user);
    }
  });

  MobileAds.instance.initialize();

  InAppPurchase.instance.purchaseStream.listen((purchaseDetailsList) {
    for (final purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.purchased) {
        handlePurchase(purchaseDetails);
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        print('Purchase error: ${purchaseDetails.error}');
      }
    }
  });

  FirebaseFirestore.instance.settings = const Settings(persistenceEnabled: true);

  runApp(const WallpaperApp());
}

class WallpaperApp extends StatelessWidget {
  const WallpaperApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'WallHammer',
      darkTheme: theme,
      themeMode: ThemeMode.dark,

      home: StreamBuilder(
        stream: FirebaseAuth.instance.authStateChanges(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          return snapshot.hasData ? const WallpaperListScreen() : const LoginScreen();
        },
      ),
    );
  }
}

