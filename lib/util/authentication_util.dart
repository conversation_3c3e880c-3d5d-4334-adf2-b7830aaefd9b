import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:wallhammer/main.dart';

import '../model/app_user.dart';


Future<User?> signUpWithEmail(String email, String password) async {
  try {
    UserCredential userCredential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
    return userCredential.user;
  } catch (e) {
    print('Sign Up Error: $e');
    return null;
  }
}

Future<User?> loginWithEmail(String email, String password) async {
  try {
    UserCredential userCredential = await FirebaseAuth.instance.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
    return userCredential.user;
  } catch (e) {
    print('Login Error: $e');
    return null;
  }
}

Future<User?> signInWithGoogle() async {
  try {
    final googleSignIn = GoogleSignIn();
    final googleUser = await googleSignIn.signIn();

    if (googleUser != null) {
      final googleAuth = await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);
      return userCredential.user;
    }
  } catch (e) {
    print('Google Sign-In Error: $e');
    print('Error details: ${e.toString()}');
    if (e.toString().contains('10:')) {
      print('DEVELOPER_ERROR: SHA-1 fingerprint or OAuth client ID mismatch');
    } else if (e.toString().contains('12501:')) {
      print('SIGN_IN_CANCELLED: User cancelled the sign-in');
    } else if (e.toString().contains('7:')) {
      print('NETWORK_ERROR: Check internet connection');
    }
    return null;
  }
  return null;
}

Future<void> handleUserRegistration(User user) async {
  final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);

  final doc = await userRef.get();
  if (!doc.exists) {
    await userRef.set({
      'coins': 3,
      'createdAt': FieldValue.serverTimestamp(),
      'subscriptionTier': 'Free',
      'subscriptionExpiry': null,
      'receivedCoinsFor': [], // Track which tiers user has received coins for
    });
  }
}

Future<AppUser?> loadAppUser(User? user) async {
  if (user == null) return null;

  final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);

  final doc = await userRef.get();
  if (doc.data() == null) return null;

  return AppUser.fromFirestore(doc.data()!);
}


Future<void> signOut() async {
  await FirebaseAuth.instance.signOut();
  final GoogleSignIn googleSignIn = GoogleSignIn();
  await googleSignIn.signOut();
}