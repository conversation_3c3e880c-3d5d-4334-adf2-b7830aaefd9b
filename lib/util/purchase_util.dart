import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:in_app_purchase/in_app_purchase.dart';


Future<List<ProductDetails>> fetchProducts() async {
  const Set<String> productIds = {'basic_tierr', 'pro_tierr', 'ultra_tierr'};

  final response = await InAppPurchase.instance.queryProductDetails(productIds);
  print(response.productDetails);
  if (response.notFoundIDs.isNotEmpty) {
    print('Some subscription tiers were not found.');
    return [];
  }

  return response.productDetails;
}

Future<void> handlePurchase(PurchaseDetails purchaseDetails) async {
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return;

  final productId = purchaseDetails.productID;
  var subscriptionTier = '';
  int coinsToAdd = 0;
  int durationInDays = 0;

  switch (productId) {
    case 'basic_tierr':
      subscriptionTier = 'Basic';
      durationInDays = 30; // 1 month
      coinsToAdd = 0; // No coins for Basic
      break;
    case 'pro_tierr':
      subscriptionTier = 'Pro';
      coinsToAdd = 30; // One-time bonus
      durationInDays = 180; // 6 months
      break;
    case 'ultra_tierr':
      subscriptionTier = 'Ultra';
      coinsToAdd = 70; // One-time bonus
      durationInDays = 365; // 12 months
      break;
  }

  final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);

  await FirebaseFirestore.instance.runTransaction((transaction) async {
    final userDoc = await transaction.get(userRef);
    if (!userDoc.exists) return;

    final currentCoins = userDoc.data()?['coins'] ?? 0;

    // Always set expiry from now (subscription replacement, not extension)
    final newExpiry = DateTime.now().add(Duration(days: durationInDays));

    // Only add coins if upgrading to a tier that provides coins
    // and user hasn't received coins for this specific tier before
    final receivedCoinsFor = List<String>.from(userDoc.data()?['receivedCoinsFor'] ?? []);
    final shouldAddCoins = coinsToAdd > 0 && !receivedCoinsFor.contains(subscriptionTier);

    final updatedReceivedCoinsFor = shouldAddCoins
        ? [...receivedCoinsFor, subscriptionTier]
        : receivedCoinsFor;

    transaction.update(userRef, {
      'subscriptionTier': subscriptionTier,
      'subscriptionExpiry': newExpiry,
      'coins': shouldAddCoins ? currentCoins + coinsToAdd : currentCoins,
      'receivedCoinsFor': updatedReceivedCoinsFor,
    });
  });

  if (purchaseDetails.pendingCompletePurchase) {
    await InAppPurchase.instance.completePurchase(purchaseDetails);
  }
}
