import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:flutter/foundation.dart';


Future<List<ProductDetails>> fetchProducts() async {
  const Set<String> productIds = {'basic_tierr', 'pro_tierr', 'ultra_tierr'};

  final response = await InAppPurchase.instance.queryProductDetails(productIds);
  print(response.productDetails);
  if (response.notFoundIDs.isNotEmpty) {
    print('Some subscription tiers were not found.');
    return [];
  }

  return response.productDetails;
}

Future<List<ProductDetails>> fetchCoinProducts() async {
  const Set<String> productIds = {'coins_10', 'coins_50', 'coins_100'};

  final response = await InAppPurchase.instance.queryProductDetails(productIds);
  print(response.productDetails);
  if (response.notFoundIDs.isNotEmpty) {
    print('Some coin products were not found.');
    return [];
  }

  return response.productDetails;
}

Future<void> handlePurchase(PurchaseDetails purchaseDetails) async {
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return;

  final productId = purchaseDetails.productID;

  // Check if this is a coin purchase
  if (productId.startsWith('coins_')) {
    await handleCoinPurchase(purchaseDetails);
    return;
  }

  // Handle subscription purchases
  var subscriptionTier = '';
  int coinsToAdd = 0;
  int durationInDays = 0;

  switch (productId) {
    case 'basic_tierr':
      subscriptionTier = 'Basic';
      durationInDays = 30; // 1 month
      coinsToAdd = 0; // No coins for Basic
      break;
    case 'pro_tierr':
      subscriptionTier = 'Pro';
      coinsToAdd = 30; // One-time bonus
      durationInDays = 180; // 6 months
      break;
    case 'ultra_tierr':
      subscriptionTier = 'Ultra';
      coinsToAdd = 70; // One-time bonus
      durationInDays = 365; // 12 months
      break;
  }

  final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);

  await FirebaseFirestore.instance.runTransaction((transaction) async {
    final userDoc = await transaction.get(userRef);
    if (!userDoc.exists) return;

    final currentCoins = userDoc.data()?['coins'] ?? 0;

    // Always set expiry from now (subscription replacement, not extension)
    final newExpiry = DateTime.now().add(Duration(days: durationInDays));

    // Only add coins if upgrading to a tier that provides coins
    // and user hasn't received coins for this specific tier before
    final receivedCoinsFor = List<String>.from(userDoc.data()?['receivedCoinsFor'] ?? []);
    final shouldAddCoins = coinsToAdd > 0 && !receivedCoinsFor.contains(subscriptionTier);

    final updatedReceivedCoinsFor = shouldAddCoins
        ? [...receivedCoinsFor, subscriptionTier]
        : receivedCoinsFor;

    transaction.update(userRef, {
      'subscriptionTier': subscriptionTier,
      'subscriptionExpiry': newExpiry,
      'coins': shouldAddCoins ? currentCoins + coinsToAdd : currentCoins,
      'receivedCoinsFor': updatedReceivedCoinsFor,
    });
  });

  if (purchaseDetails.pendingCompletePurchase) {
    await InAppPurchase.instance.completePurchase(purchaseDetails);
  }
}

Future<void> handleCoinPurchase(PurchaseDetails purchaseDetails) async {
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return;

  final productId = purchaseDetails.productID;
  int coinsToAdd = 0;

  switch (productId) {
    case 'coins_10':
      coinsToAdd = 10;
      break;
    case 'coins_50':
      coinsToAdd = 50;
      break;
    case 'coins_100':
      coinsToAdd = 100;
      break;
  }

  if (coinsToAdd > 0) {
    final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);

    await FirebaseFirestore.instance.runTransaction((transaction) async {
      final userDoc = await transaction.get(userRef);
      if (!userDoc.exists) return;

      final currentCoins = userDoc.data()?['coins'] ?? 0;

      transaction.update(userRef, {
        'coins': currentCoins + coinsToAdd,
      });
    });
  }

  if (purchaseDetails.pendingCompletePurchase) {
    await InAppPurchase.instance.completePurchase(purchaseDetails);
  }
}

/// Restore purchases and sync with Firebase
/// This should be called when user logs in or when subscription screen loads
Future<bool> restoreAndSyncPurchases() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      if (kDebugMode) print('No authenticated user for purchase restoration');
      return false;
    }

    if (kDebugMode) print('Starting purchase restoration for user: ${user.uid}');

    // Restore purchases from Play Store
    await InAppPurchase.instance.restorePurchases();

    // Listen for restored purchases through the purchase stream
    // The restored purchases will be handled by the existing purchase stream listener

    if (kDebugMode) print('Purchase restoration initiated');
    return true;
  } catch (e) {
    if (kDebugMode) print('Error during purchase restoration: $e');
    return false;
  }
}

/// Validate current subscription status against Play Store
/// This checks if the user has an active subscription that should be synced to Firebase
Future<bool> validateSubscriptionStatus() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return false;

    // Get current Firebase subscription status
    final userDoc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .get();

    if (!userDoc.exists) return false;

    final currentTier = userDoc.data()?['subscriptionTier'] ?? 'Free';
    final currentExpiry = userDoc.data()?['subscriptionExpiry'] != null
        ? (userDoc.data()!['subscriptionExpiry'] as Timestamp).toDate()
        : null;

    // If user has Free tier or expired subscription, try to restore purchases
    if (currentTier == 'Free' ||
        (currentExpiry != null && currentExpiry.isBefore(DateTime.now()))) {
      if (kDebugMode) print('User has Free/expired subscription, attempting restoration');
      return await restoreAndSyncPurchases();
    }

    // If user has active subscription, validate it's not expired
    if (currentExpiry != null && currentExpiry.isBefore(DateTime.now())) {
      if (kDebugMode) print('Subscription expired, switching to Free tier');
      await _expireSubscription(user.uid);
      return false;
    }

    return currentTier != 'Free';
  } catch (e) {
    if (kDebugMode) print('Error validating subscription status: $e');
    return false;
  }
}

/// Expire a subscription in Firebase
Future<void> _expireSubscription(String userId) async {
  await FirebaseFirestore.instance
      .collection('users')
      .doc(userId)
      .update({
    'subscriptionTier': 'Free',
    'subscriptionExpiry': null,
  });
}


