import 'package:flutter/material.dart';

final theme = ThemeData.dark(useMaterial3: true).copyWith(
  colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
  primaryColor: Colors.blue,
  appBarTheme: const AppBarTheme(
    backgroundColor: Colors.blueAccent,
  ),
  textTheme: const TextTheme(
    labelMedium: TextStyle(fontSize: 12.0, color: Colors.grey),
  ),
  inputDecorationTheme: const InputDecorationTheme(
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.blueAccent),
    ),
  ),
  snackBarTheme: const SnackBarThemeData(
    backgroundColor: Colors.blueAccent,
    contentTextStyle: TextStyle(color: Colors.white),
  ),
);