import 'package:cloud_firestore/cloud_firestore.dart';

class AppUser {
  final int coins;
  final AppUserSubscription subscriptionTier;
  final DateTime? subscriptionExpiry;

  const AppUser({
    required this.coins,
    required this.subscriptionTier,
    required this.subscriptionExpiry,
  });

  factory AppUser.fromFirestore(Map<String, dynamic> data) {
    return AppUser(
      coins: data['coins'],
      subscriptionTier: _parseSubscription(data['subscriptionTier']),
      subscriptionExpiry: data['subscriptionExpiry'] == null ? null :
        (data['subscriptionExpiry'] as Timestamp).toDate(),
    );
  }
}

enum AppUserSubscription {
  Free, Basic, Pro, Ultra
}

AppUserSubscription _parseSubscription(String text) {
  switch (text) {
    case 'Free': return AppUserSubscription.Free;
    case 'Basic': return AppUserSubscription.Basic;
    case 'Pro': return AppUserSubscription.Pro;
    case 'Ultra': return AppUserSubscription.Ultra;
  }

  throw Exception('Unhandled subscription tier: $text');
}