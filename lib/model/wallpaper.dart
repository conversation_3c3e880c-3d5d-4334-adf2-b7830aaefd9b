import 'package:firebase_storage/firebase_storage.dart';

class Wallpaper {
  final String id;
  final String filePath;
  final List<String> categories;
  final int downloadCount;
  final List<double> ratings;
  double averageRating;

  Wallpaper({
    required this.id,
    required this.filePath,
    required this.categories,
    required this.downloadCount,
    required this.ratings,
    required this.averageRating,
  });

  factory Wallpaper.fromFirestore(String id, Map<String, dynamic> data) {
    return Wallpaper(
      id: id,
      filePath: data['filePath'],
      categories: (data['categories'] as List).cast<String>(),
      downloadCount: data['downloads'],
      ratings: List.of(data['ratings'])
          .cast<double>(),
      averageRating: data['averageRating'].toDouble(),
    );
  }

  String getCategoriesCapitalized() {
    return categories
        .map((e) => '${e[0].toUpperCase()}${e.substring(1)}')
        .join(', ');
  }

  Future<String?> fetchImageUrl() async {
    final url = await FirebaseStorage.instance
        .ref(filePath)
        .getDownloadURL();
    return url;
  }
}