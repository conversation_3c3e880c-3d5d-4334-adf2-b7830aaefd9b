import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wallhammer/components/icon_text_button.dart';
import 'package:wallhammer/model/app_user.dart';
import 'package:wallhammer/screen/login_screen.dart';
import 'package:wallhammer/screen/subscription_screen.dart';
import 'package:wallhammer/util/authentication_util.dart';

import '../components/navigation_menu.dart';

class UserScreen extends StatefulWidget {
  const UserScreen({super.key});

  @override
  State<UserScreen> createState() => _UserScreenState();
}

class _UserScreenState extends State<UserScreen> {
  final debugAdId = 'ca-app-pub-3940256099942544/5224354917';
  final productionAdId = Platform.isAndroid
      ? 'ca-app-pub-9147916407761104/5137720498'
      : const String.fromEnvironment('AD_REWARD_IOS');
  final user = FirebaseAuth.instance.currentUser;

  RewardedAd? rewardedAd;
  Future<AppUser?>? loadUserFuture;

  @override
  void initState() {
    super.initState();
    _loadRewardedAd();

    loadUserFuture = loadAppUser(user);
  }

  void _loadRewardedAd() {
    final adUnitId = kDebugMode ? debugAdId : productionAdId;

    RewardedAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(
        keywords: ['wallpaper', 'mobile', 'android'],
        nonPersonalizedAds: false,
      ),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) => setState(() => rewardedAd = ad),
        onAdFailedToLoad: (error) => print('Failed to load rewarded ad: $error'),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('My Profile')),
      drawer: const NavigationMenu(),
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildInfoSection(
              icon: Icons.email,
              value: user?.email ?? 'Unknown',
            ),
            FutureBuilder(
              future: loadUserFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return _buildInfoSection(
                    icon: Icons.monetization_on,
                    value: 'Loading...',
                  );
                } else if (snapshot.hasError) {
                  return _buildInfoSection(
                    icon: Icons.monetization_on,
                    value: 'Error loading user details',
                  );
                } else {
                  final appUser = snapshot.requireData;
                  if (appUser == null) {
                    return const Text('An error occurred loading your profile.');
                  }

                  return Column(
                    children: [
                      _buildInfoSection(
                        icon: Icons.monetization_on,
                        value: '${appUser.coins} Download Coins',
                      ),
                      _buildInfoSection(
                        icon: Icons.workspace_premium,
                        value: 'Current subscription tier: ${appUser.subscriptionTier.name}',
                      ),
                      const SizedBox(height: 10.0),
                      // TODO Re-inmstantiate when subscriptions work
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => SubscriptionScreen(
                            appUser: appUser,
                            onBuySubscription: () {
                              Navigator.of(context).pop();
                              setState(() {
                                loadUserFuture = loadAppUser(user);
                              });
                            },
                          ))),
                        child: const Text('Buy a Subscription'),
                      ),
                    ],
                  );
                }
              },
            ),
            const SizedBox(height: 20.0),
            IconTextButton(
              text: 'Watch Ad to get Coin',
              icon: Icons.camera,
              onPressed: rewardedAd == null ? null : () {
                rewardedAd!.show(
                  onUserEarnedReward: (ad, reward) async {
                    await FirebaseFirestore.instance
                        .collection('users')
                        .doc(FirebaseAuth.instance.currentUser?.uid)
                        .update({'coins': FieldValue.increment(1)});
                    setState(() => loadUserFuture = loadAppUser(user));

                    rewardedAd!.dispose();
                    setState(() => rewardedAd = null);
                    _loadRewardedAd();
                  },
                );
              },
            ),

            const SizedBox(height: 20.0),
            IconTextButton(
              text: 'Logout',
              icon: Icons.logout,
              onPressed: () {
                FirebaseAuth.instance.signOut().then((_) {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (context) => const LoginScreen()),
                  );
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection({required IconData icon, required String value}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon),
          const SizedBox(width: 20.0),
          Text(value),
        ],
      ),
    );
  }
}
