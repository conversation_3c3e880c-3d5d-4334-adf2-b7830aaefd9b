import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'dart:async';

import '../model/app_user.dart';
import '../util/purchase_util.dart';
import '../components/privacy_policy_dialog.dart';
import '../components/terms_of_service_dialog.dart';

class SubscriptionScreen extends StatefulWidget {
  final AppUser? appUser;
  final void Function()? onBuySubscription;

  const SubscriptionScreen({
    this.appUser,
    this.onBuySubscription,
    super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  List<ProductDetails> _products = [];
  bool _isLoading = true;
  bool _useMockData = kDebugMode;
  String? productDetails;
  AppUser? _currentUser;
  StreamSubscription<DocumentSnapshot>? _userSubscription;
  late StreamSubscription<List<PurchaseDetails>> _purchaseSubscription;

  @override
  void initState() {
    super.initState();
    _currentUser = widget.appUser;
    _fetchProducts();
    _loadProductDetails();
    _setupUserStream();
    _setupPurchaseStream();
  }

  @override
  void dispose() {
    _userSubscription?.cancel();
    _purchaseSubscription.cancel();
    super.dispose();
  }

  void _setupUserStream() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      _userSubscription = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .snapshots()
          .listen((snapshot) {
        if (snapshot.exists && mounted) {
          setState(() {
            _currentUser = AppUser.fromFirestore(snapshot.data()!);
          });
        }
      });
    }
  }

  Future<void> _refreshUserData() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      try {
        final snapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (snapshot.exists && mounted) {
          setState(() {
            _currentUser = AppUser.fromFirestore(snapshot.data()!);
          });
        }
      } catch (e) {
        if (kDebugMode) print('Error refreshing user data: $e');
      }
    }
  }

  void _setupPurchaseStream() {
    _purchaseSubscription = InAppPurchase.instance.purchaseStream.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
          if (purchaseDetails.status == PurchaseStatus.purchased) {
            _handleSuccessfulPurchase(purchaseDetails);
          } else if (purchaseDetails.status == PurchaseStatus.error) {
            _handlePurchaseError(purchaseDetails);
          } else if (purchaseDetails.status == PurchaseStatus.pending) {
            _handlePendingPurchase(purchaseDetails);
          }
        }
      },
      onError: (error) {
        if (kDebugMode) print('Purchase stream error: $error');
      },
    );
  }

  Future<void> _loadProductDetails() async {
    const Set<String> productIds = {'basic_tier', 'pro_tier', 'ultra_tier'};
    final x = await InAppPurchase.instance.queryProductDetails(productIds);
    if (mounted) {
      setState(() => productDetails = x.productDetails.map((e) => '${e.id}, ${e.price}, ${e.title}').join(' | '));
    }
  }

  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      await handlePurchase(purchaseDetails);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase successful! Welcome to ${purchaseDetails.productID}'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onBuySubscription?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing purchase: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handlePurchaseError(PurchaseDetails purchaseDetails) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Purchase failed: ${purchaseDetails.error?.message ?? 'Unknown error'}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handlePendingPurchase(PurchaseDetails purchaseDetails) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Purchase is pending. Please wait...'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _fetchProducts() async {
    try {
      final products = await fetchProducts();
      if (products.isEmpty) {
        // Only use mock data in debug mode
        if (kDebugMode) {
          if (mounted) {
            setState(() {
              _useMockData = true;
              _products = _getMockProducts();
              _isLoading = false;
            });
          }
        } else {
          // In release mode, show error if no products found
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Subscription products not available. Please try again later.'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      } else {
        if (mounted) {
          setState(() {
            _useMockData = false;
            _products = products;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      // Only fall back to mock data in debug mode
      if (kDebugMode) {
        if (mounted) {
          setState(() {
            _useMockData = true;
            _products = _getMockProducts();
            _isLoading = false;
          });
        }
      } else {
        // In release mode, show error
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to load subscription products: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// Mock product list that you can adapt as you wish
  List<ProductDetails> _getMockProducts() {
    return [
      ProductDetails(
        id: "basic_tierr",
        title: "Basic Subscription (Wallhammer)",
        description: "Remove ads",
        price: "€4.99",
        rawPrice: 4.99,
        currencyCode: "EUR",
      ),
      ProductDetails(
        id: "pro_tierr",
        title: "Pro Subscription (Wallhammer)",
        description: "Remove ads +30 Coins on first purchase",
        price: "€39.99",
        rawPrice: 39.99,
        currencyCode: "EUR",
      ),
      ProductDetails(
        id: "ultra_tierr",
        title: "Ultra Subscription (Wallhammer)",
        description: "Remove ads +70 Coins on first purchase",
        price: "€69.99",
        rawPrice: 69.99,
        currencyCode: "EUR",
      ),
    ];
  }

  /// Perform the actual purchase flow for real IAPs
  Future<void> _buySubscription(ProductDetails product) async {
    try {
      // Only allow mock purchases in debug mode
      if (_useMockData && kDebugMode) {
        // If using mock data in debug mode, simulate the purchase
        await _mockPurchase(product.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Mock purchase successful: ${product.title}")),
          );
        }
        return;
      }

      // In release mode or with real products, proceed with real purchase flow
      if (!_useMockData) {
        // Show loading indicator
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Processing purchase...'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
        await InAppPurchase.instance.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        // In release mode with mock data, show error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Subscription products not available. Please contact support.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to initiate purchase: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Simulate setting the user's tier on mock purchase
  Future<void> _mockPurchase(String productId) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);
    final AppUserSubscription newTier = _mapProductIdToTier(productId);

    // Determine subscription duration and coin bonus
    int durationInDays = 30; // Default for Basic
    int coinsToAdd = 0;

    switch (productId) {
      case 'basic_tierr':
        durationInDays = 30; // 1 month
        coinsToAdd = 0;
        break;
      case 'pro_tierr':
        durationInDays = 180; // 6 months
        coinsToAdd = 30;
        break;
      case 'ultra_tierr':
        durationInDays = 365; // 12 months
        coinsToAdd = 70;
        break;
    }

    // Update user subscription and add coins if applicable
    await FirebaseFirestore.instance.runTransaction((transaction) async {
      final userDoc = await transaction.get(userRef);
      if (!userDoc.exists) return;

      final currentCoins = userDoc.data()?['coins'] ?? 0;

      // Always set expiry from now (subscription replacement, not extension)
      final newExpiry = DateTime.now().add(Duration(days: durationInDays));

      // Only add coins if upgrading to a tier that provides coins
      // and user hasn't received coins for this specific tier before
      final receivedCoinsFor = List<String>.from(userDoc.data()?['receivedCoinsFor'] ?? []);
      final shouldAddCoins = coinsToAdd > 0 && !receivedCoinsFor.contains(newTier.name);

      final updatedReceivedCoinsFor = shouldAddCoins
          ? [...receivedCoinsFor, newTier.name]
          : receivedCoinsFor;

      transaction.update(userRef, {
        'subscriptionTier': newTier.name,
        'subscriptionExpiry': newExpiry,
        'coins': shouldAddCoins ? currentCoins + coinsToAdd : currentCoins,
        'receivedCoinsFor': updatedReceivedCoinsFor,
      });
    });

    widget.onBuySubscription?.call();
  }

  Future<void> _switchToFreeTier() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);

    await userRef.update({
      'subscriptionTier': 'Free',
      'subscriptionExpiry': null,
      'receivedCoinsFor': [], // Reset coin tracking when switching to free
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Switched to Free tier (Debug Mode)")),
      );
    }
    widget.onBuySubscription?.call();
  }

  AppUserSubscription _mapProductIdToTier(String productId) {
    switch (productId) {
      case 'basic_tierr':
        return AppUserSubscription.Basic;
      case 'pro_tierr':
        return AppUserSubscription.Pro;
      case 'ultra_tierr':
        return AppUserSubscription.Ultra;
      default:
        return AppUserSubscription.Free;
    }
  }

  bool _isLowerOrSameTier(String productId, AppUserSubscription currentTier) {
    final tierOrder = {
      "basic_tierr": AppUserSubscription.Basic,
      "pro_tierr": AppUserSubscription.Pro,
      "ultra_tierr": AppUserSubscription.Ultra,
    };

    final newTier = tierOrder[productId];
    if (newTier == null) {
      // If unknown, treat it as disabled
      return true;
    }
    return newTier.index <= currentTier.index;
  }

  Widget _buildSubscriptionCard(ProductDetails product) {
    final currentUser = _currentUser ?? AppUser(coins: 0, subscriptionTier: AppUserSubscription.Free, subscriptionExpiry: null);
    final isLockedOut = _isLowerOrSameTier(product.id, currentUser.subscriptionTier);
    final isCurrentPlan = currentUser.subscriptionTier == _mapProductIdToTier(product.id);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 4,
      color: Colors.grey[900], // Dark background for the card
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isCurrentPlan 
          ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
          : BorderSide(color: Colors.grey[700]!, width: 1),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isCurrentPlan)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  "CURRENT PLAN",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            if (isCurrentPlan) const SizedBox(height: 12),
            
            Text(
              product.title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isLockedOut ? Colors.grey[400] : Colors.white,
              ),
            ),
            
            const SizedBox(height: 8),
            Text(
              product.description,
              style: TextStyle(
                color: isLockedOut ? Colors.grey[500] : Colors.grey[300],
                fontSize: 14,
              ),
            ),
            
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  product.price,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isLockedOut ? Colors.grey[400] : Colors.white,
                  ),
                ),
                if (!isLockedOut && !isCurrentPlan)
                  ElevatedButton(
                    onPressed: () => _buySubscription(product),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text("Subscribe"),
                  )
                else if (isCurrentPlan)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.green[600],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      "Active",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _currentUser ?? AppUser(coins: 0, subscriptionTier: AppUserSubscription.Free, subscriptionExpiry: null);

    return Scaffold(
      appBar: AppBar(
        title: const Text("Premium Subscription", style: TextStyle(color: Colors.white),),
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white, size: 28),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Skip',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshUserData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                  // Header Section
                  Container(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Upgrade to Premium",
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          "Unlock unlimited access to premium wallpapers and enjoy an ad-free experience",
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Real-time User Info
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.account_circle, color: Colors.blue, size: 32),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Current Coins: ${currentUser.coins}",
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                "Plan: ${currentUser.subscriptionTier.name}",
                                style: TextStyle(
                                  color: Colors.blue[300],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (currentUser.subscriptionTier != AppUserSubscription.Free)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              "PREMIUM",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Current Plan Status
                  if (currentUser.subscriptionTier != AppUserSubscription.Free)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Current Plan: ${currentUser.subscriptionTier.name}",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green[700],
                                  ),
                                ),
                                if (currentUser.subscriptionExpiry != null)
                                  Text(
                                    'Expires: ${DateFormat('dd.MM.yyyy').format(currentUser.subscriptionExpiry!)}',
                                    style: TextStyle(
                                      color: Colors.green[600],
                                      fontSize: 12,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                  const SizedBox(height: 24),

                  // Features Section
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Premium Features",
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildFeatureItem(Icons.wallpaper, "Unlimited Premium Wallpapers"),
                        _buildFeatureItem(Icons.block, "Ad-Free Experience"),
                        _buildFeatureItem(Icons.download, "Unlimited Downloads"),
                        _buildFeatureItem(Icons.high_quality, "High Quality Images"),
                        _buildFeatureItem(Icons.new_releases, "Early Access to New Content"),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Subscription Plans
                  ..._products.map((product) => _buildSubscriptionCard(product)),

                  const SizedBox(height: 24),

                  // Debug Info (only in debug mode)
                  if (kDebugMode) ...[
                    Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Debug Information",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            productDetails?.isEmpty ?? true 
                              ? 'No product details loaded' 
                              : productDetails!,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Terms and RR® Section
                  Container(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        Text(
                          "By subscribing, you agree to our Terms of Service and Privacy Policy. "
                          "Subscriptions automatically renew unless auto-renew is turned off at least 24 hours before the end of the current period. "
                          "You can manage your subscriptions in your device's account settings. "
                          "Payment will be charged to your Google Play account at confirmation of purchase.",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white70,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Row(
                           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                           children: [
                             TextButton(
                               onPressed: () {
                                 showDialog(
                                   context: context,
                                   builder: (context) => const TermsOfServiceDialog(),
                                 );
                               },
                               child: const Text(
                                 "Terms of Service",
                                 style: TextStyle(color: Colors.white70),
                               ),
                             ),
                             TextButton(
                               onPressed: () {
                                 showDialog(
                                   context: context,
                                   builder: (context) => const PrivacyPolicyDialog(),
                                 );
                               },
                               child: const Text(
                                 "Privacy Policy",
                                 style: TextStyle(color: Colors.white70),
                               ),
                             ),
                           ],
                         ),
                         const SizedBox(height: 16),
                         // Continue without Premium button
                         TextButton(
                           onPressed: () => Navigator.of(context).pop(),
                           child: const Text(
                             "Continue without Premium",
                             style: TextStyle(
                               color: Colors.white70,
                               fontSize: 16,
                               decoration: TextDecoration.underline,
                             ),
                           ),
                         ),
                      ],
                    ),
                  ),

                  // Debug Controls (only in debug mode)
                  if (kDebugMode) ...[
                    const Divider(),
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          const Text(
                            "Debug Controls",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 12),
                          ElevatedButton(
                            onPressed: _switchToFreeTier,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text("Debug: Switch to Free"),
                          ),
                        ],
                      ),
                    ),
                  ],
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
