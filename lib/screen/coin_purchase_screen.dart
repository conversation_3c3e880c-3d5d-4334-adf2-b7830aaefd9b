import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';

import '../model/app_user.dart';
import '../util/purchase_util.dart';

class CoinPurchaseScreen extends StatefulWidget {
  final AppUser? appUser;
  final void Function()? onCoinPurchase;

  const CoinPurchaseScreen({
    this.appUser,
    this.onCoinPurchase,
    super.key,
  });

  @override
  State<CoinPurchaseScreen> createState() => _CoinPurchaseScreenState();
}

class _CoinPurchaseScreenState extends State<CoinPurchaseScreen> {
  late StreamSubscription<List<PurchaseDetails>> _purchaseSubscription;
  List<ProductDetails> _products = [];
  bool _isLoading = true;
  AppUser? _currentUser;
  
  // Mock data flag - set to true for testing, false for production
  final bool _useMockData = kDebugMode;

  @override
  void initState() {
    super.initState();
    _currentUser = widget.appUser;
    _setupPurchaseStream();
    _loadCoinProducts();
  }

  @override
  void dispose() {
    _purchaseSubscription.cancel();
    super.dispose();
  }

  void _setupPurchaseStream() {
    _purchaseSubscription = InAppPurchase.instance.purchaseStream.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
          if (purchaseDetails.status == PurchaseStatus.purchased) {
            _handleSuccessfulPurchase(purchaseDetails);
          } else if (purchaseDetails.status == PurchaseStatus.error) {
            _handlePurchaseError(purchaseDetails);
          } else if (purchaseDetails.status == PurchaseStatus.pending) {
            _handlePendingPurchase(purchaseDetails);
          }
        }
      },
      onError: (error) {
        if (kDebugMode) print('Purchase stream error: $error');
      },
    );
  }

  Future<void> _loadCoinProducts() async {
    setState(() => _isLoading = true);
    
    try {
      if (_useMockData) {
        // Use mock data for testing
        _products = _getMockCoinProducts();
      } else {
        // Load real products
        _products = await fetchCoinProducts();
      }
    } catch (e) {
      if (kDebugMode) print('Error loading coin products: $e');
      _products = [];
    }
    
    setState(() => _isLoading = false);
  }

  List<ProductDetails> _getMockCoinProducts() {
    return [
      ProductDetails(
        id: "coins_10",
        title: "10 Coins",
        description: "Get 10 download coins",
        price: "€4.99",
        rawPrice: 4.99,
        currencyCode: "EUR",
      ),
      ProductDetails(
        id: "coins_50",
        title: "50 Coins",
        description: "Get 50 download coins",
        price: "€39.99",
        rawPrice: 39.99,
        currencyCode: "EUR",
      ),
      ProductDetails(
        id: "coins_100",
        title: "100 Coins",
        description: "Get 100 download coins",
        price: "€69.99",
        rawPrice: 69.99,
        currencyCode: "EUR",
      ),
    ];
  }

  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      await handleCoinPurchase(purchaseDetails);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Coins purchased successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onCoinPurchase?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing purchase: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handlePurchaseError(PurchaseDetails purchaseDetails) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Purchase failed: ${purchaseDetails.error?.message ?? 'Unknown error'}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handlePendingPurchase(PurchaseDetails purchaseDetails) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Purchase is pending...'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _buyCoinPack(ProductDetails product) async {
    try {
      if (_useMockData && kDebugMode) {
        // Mock purchase for testing
        await _mockCoinPurchase(product.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Mock purchase successful: ${product.title}")),
          );
        }
        return;
      }

      if (!_useMockData) {
        // Real purchase
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Processing purchase...'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
        await InAppPurchase.instance.buyConsumable(purchaseParam: purchaseParam);
      } else {
        // In release mode with mock data, show error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Coin products not available. Please contact support.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _mockCoinPurchase(String productId) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    int coinsToAdd = 0;
    switch (productId) {
      case 'coins_10':
        coinsToAdd = 10;
        break;
      case 'coins_50':
        coinsToAdd = 50;
        break;
      case 'coins_100':
        coinsToAdd = 100;
        break;
    }

    if (coinsToAdd > 0) {
      final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);
      
      await FirebaseFirestore.instance.runTransaction((transaction) async {
        final userDoc = await transaction.get(userRef);
        if (!userDoc.exists) return;

        final currentCoins = userDoc.data()?['coins'] ?? 0;
        
        transaction.update(userRef, {
          'coins': currentCoins + coinsToAdd,
        });
      });
    }

    widget.onCoinPurchase?.call();
  }

  int _getCoinAmount(String productId) {
    switch (productId) {
      case 'coins_10':
        return 10;
      case 'coins_50':
        return 50;
      case 'coins_100':
        return 100;
      default:
        return 0;
    }
  }

  Widget _buildCoinPackCard(ProductDetails product) {
    final coinAmount = _getCoinAmount(product.id);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 4,
      color: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey[700]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber[600],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.monetization_on,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$coinAmount Coins',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Download $coinAmount wallpapers',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      product.price,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () => _buyCoinPack(product),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text("Buy"),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _currentUser ?? AppUser(coins: 0, subscriptionTier: AppUserSubscription.Free, subscriptionExpiry: null);

    return Scaffold(
      appBar: AppBar(
        title: const Text("Buy Coins", style: TextStyle(color: Colors.white)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white, size: 28),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  // Current coins display
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.grey[850],
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.monetization_on,
                          color: Colors.amber,
                          size: 32,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Current Coins: ${currentUser.coins}',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),
                  // Coin packs
                  if (_products.isEmpty)
                    const Padding(
                      padding: EdgeInsets.all(20),
                      child: Text(
                        'No coin packs available at the moment.',
                        style: TextStyle(color: Colors.white70),
                        textAlign: TextAlign.center,
                      ),
                    )
                  else
                    ..._products.map((product) => _buildCoinPackCard(product)),
                  const SizedBox(height: 20),
                ],
              ),
            ),
    );
  }
}
