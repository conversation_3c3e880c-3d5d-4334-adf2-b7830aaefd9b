import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';

import '../model/app_user.dart';
import '../util/purchase_util.dart';
import '../components/privacy_policy_dialog.dart';
import '../components/terms_of_service_dialog.dart';

class CoinPurchaseScreen extends StatefulWidget {
  final AppUser? appUser;
  final void Function()? onCoinPurchase;

  const CoinPurchaseScreen({
    this.appUser,
    this.onCoinPurchase,
    super.key,
  });

  @override
  State<CoinPurchaseScreen> createState() => _CoinPurchaseScreenState();
}

class _CoinPurchaseScreenState extends State<CoinPurchaseScreen> {
  late StreamSubscription<List<PurchaseDetails>> _purchaseSubscription;
  List<ProductDetails> _products = [];
  bool _isLoading = true;
  AppUser? _currentUser;
  
  // Mock data flag - set to true for testing, false for production
  final bool _useMockData = kDebugMode;

  @override
  void initState() {
    super.initState();
    _currentUser = widget.appUser;
    _setupPurchaseStream();
    _loadCoinProducts();
  }

  @override
  void dispose() {
    _purchaseSubscription.cancel();
    super.dispose();
  }

  void _setupPurchaseStream() {
    _purchaseSubscription = InAppPurchase.instance.purchaseStream.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
          if (purchaseDetails.status == PurchaseStatus.purchased) {
            _handleSuccessfulPurchase(purchaseDetails);
          } else if (purchaseDetails.status == PurchaseStatus.error) {
            _handlePurchaseError(purchaseDetails);
          } else if (purchaseDetails.status == PurchaseStatus.pending) {
            _handlePendingPurchase(purchaseDetails);
          }
        }
      },
      onError: (error) {
        if (kDebugMode) print('Purchase stream error: $error');
      },
    );
  }

  Future<void> _loadCoinProducts() async {
    setState(() => _isLoading = true);
    
    try {
      if (_useMockData) {
        // Use mock data for testing
        _products = _getMockCoinProducts();
      } else {
        // Load real products
        _products = await fetchCoinProducts();
      }
    } catch (e) {
      if (kDebugMode) print('Error loading coin products: $e');
      _products = [];
    }
    
    setState(() => _isLoading = false);
  }

  List<ProductDetails> _getMockCoinProducts() {
    return [
      ProductDetails(
        id: "coins_10",
        title: "10 Coins",
        description: "Get 10 download coins",
        price: "€4.99",
        rawPrice: 4.99,
        currencyCode: "EUR",
      ),
      ProductDetails(
        id: "coins_50",
        title: "50 Coins",
        description: "Get 50 download coins",
        price: "€39.99",
        rawPrice: 39.99,
        currencyCode: "EUR",
      ),
      ProductDetails(
        id: "coins_100",
        title: "100 Coins",
        description: "Get 100 download coins",
        price: "€69.99",
        rawPrice: 69.99,
        currencyCode: "EUR",
      ),
    ];
  }

  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      await handleCoinPurchase(purchaseDetails);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Coins purchased successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onCoinPurchase?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing purchase: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handlePurchaseError(PurchaseDetails purchaseDetails) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Purchase failed: ${purchaseDetails.error?.message ?? 'Unknown error'}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handlePendingPurchase(PurchaseDetails purchaseDetails) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Purchase is pending...'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _buyCoinPack(ProductDetails product) async {
    try {
      if (_useMockData && kDebugMode) {
        // Mock purchase for testing
        await _mockCoinPurchase(product.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Mock purchase successful: ${product.title}")),
          );
        }
        return;
      }

      if (!_useMockData) {
        // Real purchase
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Processing purchase...'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
        await InAppPurchase.instance.buyConsumable(purchaseParam: purchaseParam);
      } else {
        // In release mode with mock data, show error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Coin products not available. Please contact support.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _mockCoinPurchase(String productId) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    int coinsToAdd = 0;
    switch (productId) {
      case 'coins_10':
        coinsToAdd = 10;
        break;
      case 'coins_50':
        coinsToAdd = 50;
        break;
      case 'coins_100':
        coinsToAdd = 100;
        break;
    }

    if (coinsToAdd > 0) {
      final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);
      
      await FirebaseFirestore.instance.runTransaction((transaction) async {
        final userDoc = await transaction.get(userRef);
        if (!userDoc.exists) return;

        final currentCoins = userDoc.data()?['coins'] ?? 0;
        
        transaction.update(userRef, {
          'coins': currentCoins + coinsToAdd,
        });
      });
    }

    widget.onCoinPurchase?.call();
  }

  int _getCoinAmount(String productId) {
    switch (productId) {
      case 'coins_10':
        return 10;
      case 'coins_50':
        return 50;
      case 'coins_100':
        return 100;
      default:
        return 0;
    }
  }

  Widget _buildCoinPackCard(ProductDetails product) {
    final coinAmount = _getCoinAmount(product.id);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 4,
      color: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey[700]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber[600],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.monetization_on,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$coinAmount Coins',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Download $coinAmount wallpapers',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      product.price,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () => _buyCoinPack(product),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text("Buy"),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _currentUser ?? AppUser(coins: 0, subscriptionTier: AppUserSubscription.Free, subscriptionExpiry: null);

    return Scaffold(
      appBar: AppBar(
        title: const Text("Buy Coins", style: TextStyle(color: Colors.white)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white, size: 28),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Skip',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshUserData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Section
                    Container(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Buy Coins",
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            "Purchase coins to download premium wallpapers and unlock exclusive content",
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Current Coins Display
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.amber.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.monetization_on, color: Colors.amber, size: 32),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Current Coins: ${currentUser.coins}",
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 18,
                                  ),
                                ),
                                const Text(
                                  "Use coins to download wallpapers",
                                  style: TextStyle(
                                    color: Colors.white70,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Features Section
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "What You Can Do With Coins",
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildFeatureItem(Icons.download, "Download Premium Wallpapers"),
                          _buildFeatureItem(Icons.high_quality, "Access High Resolution Images"),
                          _buildFeatureItem(Icons.collections, "Unlock Exclusive Collections"),
                          _buildFeatureItem(Icons.new_releases, "Get Latest Wallpaper Releases"),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Coin Packages
                    if (_products.isEmpty)
                      const Padding(
                        padding: EdgeInsets.all(20),
                        child: Text(
                          'No coin packs available at the moment.',
                          style: TextStyle(color: Colors.white70),
                          textAlign: TextAlign.center,
                        ),
                      )
                    else
                      ..._products.map((product) => _buildCoinPackCard(product)),

                    const SizedBox(height: 24),

                    // Debug Info (only in debug mode)
                    if (kDebugMode) ...[
                      Container(
                        padding: const EdgeInsets.all(16),
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              "Debug Information",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _useMockData ? 'Using mock coin products' : 'Using real coin products',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Terms and Privacy Section
                    Container(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          Text(
                            "By purchasing coins, you agree to our Terms of Service and Privacy Policy. "
                            "Coins are consumable items that can be used to download wallpapers. "
                            "Payment will be charged to your account at confirmation of purchase. "
                            "Coins do not expire and are tied to your account.",
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white70,
                              height: 1.4,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              TextButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => const TermsOfServiceDialog(),
                                  );
                                },
                                child: const Text(
                                  "Terms of Service",
                                  style: TextStyle(color: Colors.white70),
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => const PrivacyPolicyDialog(),
                                  );
                                },
                                child: const Text(
                                  "Privacy Policy",
                                  style: TextStyle(color: Colors.white70),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Continue without purchasing button
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text(
                              "Continue without purchasing",
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 16,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Future<void> _refreshUserData() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      try {
        final snapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (snapshot.exists && mounted) {
          setState(() {
            _currentUser = AppUser.fromFirestore(snapshot.data()!);
          });
        }
      } catch (e) {
        if (kDebugMode) print('Error refreshing user data: $e');
      }
    }
  }

  Widget _buildFeatureItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.amber,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
